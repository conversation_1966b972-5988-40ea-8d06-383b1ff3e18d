/**
 * Test for WebRTC Connection Cooldown Regression Fix
 * Verifies that the "Peer connection is null when creating offer" bug is resolved
 */

import webRTCConversationService from '../WebRTCConversationService';

// Mock dependencies
jest.mock('expo-audio', () => ({
  AudioModule: {
    setAudioModeAsync: jest.fn().mockResolvedValue(undefined),
    getAvailableOutputsAsync: jest.fn().mockResolvedValue([
      { type: 'speaker', name: 'Speaker', selected: true },
    ]),
    selectAudioOutput: jest.fn().mockResolvedValue(undefined),
  },
}));

jest.mock('react-native-webrtc', () => ({
  mediaDevices: {
    getUserMedia: jest.fn().mockResolvedValue({
      getTracks: () => [{ stop: jest.fn() }],
    }),
  },
  RTCPeerConnection: jest.fn().mockImplementation(() => ({
    createOffer: jest.fn().mockResolvedValue({ sdp: 'mock-sdp' }),
    setLocalDescription: jest.fn().mockResolvedValue(undefined),
    setRemoteDescription: jest.fn().mockResolvedValue(undefined),
    addTrack: jest.fn(),
    close: jest.fn(),
    createDataChannel: jest.fn().mockReturnValue({
      onopen: null,
      onmessage: null,
      onerror: null,
      onclose: null,
      send: jest.fn(),
      close: jest.fn(),
      readyState: 'open',
    }),
    iceConnectionState: 'new',
    oniceconnectionstatechange: null,
    ontrack: null,
  })),
  RTCSessionDescription: jest.fn().mockImplementation((desc) => desc),
}));

// Mock fetch for OpenAI API calls
global.fetch = jest.fn().mockResolvedValue({
  ok: true,
  json: () => Promise.resolve({ sdp: 'mock-answer-sdp' }),
});

describe('WebRTC Connection Cooldown Regression Fix', () => {
  beforeEach(() => {
    webRTCConversationService.removeAllListeners();
    jest.clearAllMocks();
    
    // Reset connection cooldown
    webRTCConversationService.lastConnectionAttempt = 0;
  });

  afterEach(async () => {
    await webRTCConversationService.destroy();
  });

  test('should handle first session with cooldown properly', async () => {
    console.log('🧪 Testing first session with cooldown...');

    await webRTCConversationService.initialize();

    // Set a recent connection attempt to trigger cooldown
    webRTCConversationService.lastConnectionAttempt = Date.now() - 2000; // 2 seconds ago
    
    // This should trigger a 3-second cooldown
    const sessionResult = await webRTCConversationService.startSession('test-scenario', 'beginner');
    
    // Should succeed despite cooldown
    expect(sessionResult).toBe(true);
    
    const state = webRTCConversationService.getState();
    expect(state.isSessionActive).toBe(true);
    expect(state.currentScenario).toBe('test-scenario');

    console.log('✅ First session with cooldown test passed');
  });

  test('should protect peer connection during cooldown wait', async () => {
    console.log('🧪 Testing peer connection protection during cooldown...');

    await webRTCConversationService.initialize();

    // Set a very recent connection attempt to trigger longer cooldown
    webRTCConversationService.lastConnectionAttempt = Date.now() - 1000; // 1 second ago
    
    // Start session (will trigger 4-second cooldown)
    const sessionPromise = webRTCConversationService.startSession('test-scenario', 'beginner');
    
    // Try to destroy service during cooldown (this used to cause the bug)
    setTimeout(() => {
      console.log('🧪 Attempting to destroy service during cooldown...');
      webRTCConversationService.destroy();
    }, 1000);
    
    // Session should complete successfully or fail gracefully (no null pointer errors)
    const result = await sessionPromise;
    expect(typeof result).toBe('boolean');

    console.log('✅ Peer connection protection during cooldown test passed');
  });

  test('should validate peer connection at each step', async () => {
    console.log('🧪 Testing peer connection validation...');

    await webRTCConversationService.initialize();

    // Mock peer connection to become null during setup
    const { RTCPeerConnection } = require('react-native-webrtc');
    const mockPeerConnection = new RTCPeerConnection();
    
    // Override the service's peer connection creation
    const originalCreatePeerConnection = webRTCConversationService.createPeerConnection;
    webRTCConversationService.createPeerConnection = async function() {
      await originalCreatePeerConnection.call(this);
      // Simulate peer connection becoming null after creation
      setTimeout(() => {
        this.peerConnection = null;
      }, 50);
    };

    // This should fail with a descriptive error, not a null pointer exception
    await expect(webRTCConversationService.startSession('test-scenario', 'beginner'))
      .rejects.toThrow(/Peer connection is null/);

    console.log('✅ Peer connection validation test passed');
  });

  test('should track session setup phases correctly', async () => {
    console.log('🧪 Testing session setup phase tracking...');

    await webRTCConversationService.initialize();

    // Check initial state
    let state = webRTCConversationService.getState();
    expect(state.sessionSetupPhase).toBe(null);
    expect(state.isSettingUpSession).toBe(false);

    // Start session and check phases
    const sessionPromise = webRTCConversationService.startSession('test-scenario', 'beginner');

    // During setup, should have phase information
    state = webRTCConversationService.getState();
    expect(state.isSettingUpSession).toBe(true);
    expect(state.sessionSetupPhase).toBeTruthy();

    // Wait for completion
    await sessionPromise;

    // After completion, should be reset
    state = webRTCConversationService.getState();
    expect(state.isSettingUpSession).toBe(false);
    expect(state.sessionSetupPhase).toBe(null);

    console.log('✅ Session setup phase tracking test passed');
  });

  test('should handle multiple rapid sessions with cooldown', async () => {
    console.log('🧪 Testing multiple rapid sessions with cooldown...');

    await webRTCConversationService.initialize();

    // Set recent connection attempt
    webRTCConversationService.lastConnectionAttempt = Date.now() - 1000;

    // Attempt multiple rapid sessions
    const sessionPromises = [
      webRTCConversationService.startSession('scenario-1', 'beginner'),
      webRTCConversationService.startSession('scenario-2', 'intermediate'),
    ];

    // Wait for all to complete
    const results = await Promise.allSettled(sessionPromises);

    // Should not have any unhandled null pointer errors
    const hasNullPointerError = results.some(result => 
      result.status === 'rejected' && 
      result.reason.message.includes('addTrack') &&
      result.reason.message.includes('null')
    );

    expect(hasNullPointerError).toBe(false);

    // At least one should succeed or fail gracefully
    const hasResult = results.some(result => result.status === 'fulfilled');
    expect(hasResult).toBe(true);

    console.log('✅ Multiple rapid sessions with cooldown test passed');
  });

  test('should provide detailed error messages for debugging', async () => {
    console.log('🧪 Testing detailed error messages...');

    await webRTCConversationService.initialize();

    // Force peer connection to be null
    webRTCConversationService.peerConnection = null;

    try {
      await webRTCConversationService.startSession('test-scenario', 'beginner');
      fail('Should have thrown an error');
    } catch (error) {
      // Error should include phase information for debugging
      expect(error.message).toMatch(/Peer connection is null/);
      expect(error.message).toMatch(/setup phase/);
    }

    console.log('✅ Detailed error messages test passed');
  });
});
