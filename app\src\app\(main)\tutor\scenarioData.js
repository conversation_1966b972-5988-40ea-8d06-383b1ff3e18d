// Static list of all learning scenarios for the tutor
export const learningScenarios = [
  { id: 's1', title: 'Greetings & Introductions', description: 'Learn how to introduce yourself and greet others', level: 'Beginner', icon: 'hand-left-outline', color: '#10B981', completed: false },
  { id: 's2', title: 'Ordering Food', description: 'Practice ordering food at a restaurant', level: 'Beginner', icon: 'restaurant-outline', color: '#3B82F6', completed: false },
  { id: 's3', title: 'Making Plans', description: 'Learn how to make plans with friends', level: 'Intermediate', icon: 'calendar-outline', color: '#F59E0B', completed: false },
  { id: 's4', title: 'Shopping Assistance', description: 'Learn to ask for help and find items while shopping in Korean stores', level: 'Intermediate', icon: 'bag-outline', color: '#4CAF50', completed: false },
  { id: 's5', title: 'Travel & Directions', description: 'Navigate and ask for directions in Korean cities and transportation', level: 'Intermediate', icon: 'map-outline', color: '#FB923C', completed: false },
  { id: 's6', title: 'Business Meeting', description: 'Practice formal business conversations, meetings, and professional language', level: 'Advanced', icon: 'briefcase-outline', color: '#3B82F6', completed: false },
  { id: 's7', title: 'Family Conversation', description: 'Talk about family members, relationships, and family activities in Korean', level: 'Beginner', icon: 'people-outline', color: '#8B5CF6', completed: false },
  { id: 's8', title: 'Hobby Discussion', description: 'Discuss personal interests, hobbies, and leisure activities in Korean', level: 'Intermediate', icon: 'game-controller-outline', color: '#A0522D', completed: false },
  { id: 's9', title: 'Job Interviews', description: 'Practice common interview questions and responses', level: 'Advanced', icon: 'briefcase-outline', color: '#EF4444', completed: false },
  { id: 's10', title: 'Meetings', description: 'Language for participating in and leading business meetings', level: 'Advanced', icon: 'people-circle-outline', color: '#14B8A6', completed: false },
  { id: 's11', title: 'Resumes', description: 'Learn how to write and discuss resume entries and qualifications', level: 'Advanced', icon: 'document-text-outline', color: '#6366F1', completed: false },
  { id: 's12', title: 'Group Projects', description: 'Discuss roles, deadlines, and collaboration in group projects', level: 'Intermediate', icon: 'people-outline', color: '#FB923C', completed: false },
  { id: 's13', title: 'Dorm Life', description: 'Conversational practice about living in a dormitory, roommates, and daily routines', level: 'Beginner', icon: 'home-outline', color: '#4CAF50', completed: false },
  { id: 's14', title: 'Buying Tickets', description: 'Practice dialogues for buying tickets at stations or cinemas', level: 'Beginner', icon: 'ticket-outline', color: '#8B5CF6', completed: false },
  { id: 's15', title: 'Going to a Café', description: 'Order drinks, ask about menu items, and chat in a café', level: 'Beginner', icon: 'cafe-outline', color: '#A0522D', completed: false },
];
