/**
 * Test for WebRTC Race Condition Fix
 * Verifies that the "Cannot read property 'addTrack' of null" bug is resolved
 */

import webRTCConversationService from '../WebRTCConversationService';

// Mock dependencies
jest.mock('expo-audio', () => ({
  AudioModule: {
    setAudioModeAsync: jest.fn().mockResolvedValue(undefined),
    getAvailableOutputsAsync: jest.fn().mockResolvedValue([
      { type: 'speaker', name: 'Speaker', selected: true },
    ]),
    selectAudioOutput: jest.fn().mockResolvedValue(undefined),
  },
}));

jest.mock('react-native-webrtc', () => ({
  mediaDevices: {
    getUserMedia: jest.fn().mockResolvedValue({
      getTracks: () => [{ stop: jest.fn() }],
    }),
  },
  RTCPeerConnection: jest.fn().mockImplementation(() => ({
    createOffer: jest.fn().mockResolvedValue({ sdp: 'mock-sdp' }),
    setLocalDescription: jest.fn().mockResolvedValue(undefined),
    setRemoteDescription: jest.fn().mockResolvedValue(undefined),
    addTrack: jest.fn(),
    close: jest.fn(),
    createDataChannel: jest.fn().mockReturnValue({
      onopen: null,
      onmessage: null,
      onerror: null,
      onclose: null,
      send: jest.fn(),
      close: jest.fn(),
      readyState: 'open',
    }),
    iceConnectionState: 'new',
    oniceconnectionstatechange: null,
    ontrack: null,
  })),
  RTCSessionDescription: jest.fn().mockImplementation((desc) => desc),
}));

describe('WebRTC Race Condition Fix', () => {
  beforeEach(() => {
    webRTCConversationService.removeAllListeners();
    jest.clearAllMocks();
  });

  afterEach(async () => {
    await webRTCConversationService.destroy();
  });

  test('should prevent race condition during session setup', async () => {
    console.log('🧪 Testing race condition prevention...');

    await webRTCConversationService.initialize();

    // Start first session
    const session1 = await webRTCConversationService.startSession('scenario-1', 'beginner');
    expect(session1).toBe(true);

    // Stop first session
    await webRTCConversationService.stopSessionByUser();

    // Simulate rapid second session start (this used to cause the race condition)
    const session2Promise = webRTCConversationService.startSession('scenario-2', 'intermediate');

    // Simulate navigation blur event that used to trigger immediate cleanup
    // This should now be protected by the session setup flag
    const destroyPromise = webRTCConversationService.destroy();

    // Both operations should complete without error
    const [session2Result] = await Promise.all([session2Promise, destroyPromise]);

    // The session should either succeed or fail gracefully (no null pointer errors)
    expect(typeof session2Result).toBe('boolean');

    console.log('✅ Race condition prevention test passed');
  });

  test('should protect peerConnection during setupLocalStream', async () => {
    console.log('🧪 Testing peerConnection protection...');

    await webRTCConversationService.initialize();

    // Mock getUserMedia to introduce delay
    const { mediaDevices } = require('react-native-webrtc');
    mediaDevices.getUserMedia.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({
        getTracks: () => [{ stop: jest.fn() }],
      }), 100))
    );

    // Start session setup
    const sessionPromise = webRTCConversationService.startSession('test-scenario', 'beginner');

    // Try to destroy service during setup (this used to cause null pointer)
    setTimeout(() => {
      webRTCConversationService.destroy();
    }, 50);

    // Should not throw "Cannot read property 'addTrack' of null"
    await expect(sessionPromise).resolves.toBeDefined();

    console.log('✅ PeerConnection protection test passed');
  });

  test('should handle session setup state correctly', async () => {
    console.log('🧪 Testing session setup state management...');

    await webRTCConversationService.initialize();

    // Check initial state
    let state = webRTCConversationService.getState();
    expect(state.isSettingUpSession).toBe(false);

    // Start session and check setup state
    const sessionPromise = webRTCConversationService.startSession('test-scenario', 'beginner');

    // During setup, flag should be true
    state = webRTCConversationService.getState();
    expect(state.isSettingUpSession).toBe(true);

    // Wait for completion
    await sessionPromise;

    // After setup, flag should be false
    state = webRTCConversationService.getState();
    expect(state.isSettingUpSession).toBe(false);

    console.log('✅ Session setup state management test passed');
  });

  test('should wait for session setup completion before destroying', async () => {
    console.log('🧪 Testing destroy wait mechanism...');

    await webRTCConversationService.initialize();

    // Mock a slow session setup
    const { mediaDevices } = require('react-native-webrtc');
    mediaDevices.getUserMedia.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({
        getTracks: () => [{ stop: jest.fn() }],
      }), 200))
    );

    // Start session setup
    const sessionPromise = webRTCConversationService.startSession('test-scenario', 'beginner');

    // Start destroy immediately
    const destroyStartTime = Date.now();
    const destroyPromise = webRTCConversationService.destroy();

    // Wait for both to complete
    await Promise.all([sessionPromise, destroyPromise]);

    const destroyEndTime = Date.now();
    const destroyDuration = destroyEndTime - destroyStartTime;

    // Destroy should have waited for session setup (at least 200ms)
    expect(destroyDuration).toBeGreaterThan(150);

    console.log('✅ Destroy wait mechanism test passed');
  });

  test('should handle multiple rapid session attempts gracefully', async () => {
    console.log('🧪 Testing multiple rapid session attempts...');

    await webRTCConversationService.initialize();

    // Attempt multiple rapid session starts
    const sessionPromises = [
      webRTCConversationService.startSession('scenario-1', 'beginner'),
      webRTCConversationService.startSession('scenario-2', 'intermediate'),
      webRTCConversationService.startSession('scenario-3', 'advanced'),
    ];

    // All should complete without throwing errors
    const results = await Promise.allSettled(sessionPromises);

    // At least one should succeed, none should throw unhandled errors
    const hasSuccess = results.some(result => result.status === 'fulfilled' && result.value === true);
    const hasUnhandledError = results.some(result => 
      result.status === 'rejected' && 
      result.reason.message.includes('addTrack')
    );

    expect(hasSuccess).toBe(true);
    expect(hasUnhandledError).toBe(false);

    console.log('✅ Multiple rapid session attempts test passed');
  });
});
