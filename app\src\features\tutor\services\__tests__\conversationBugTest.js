/**
 * Manual Test for WebRTC Conversation "Initializing" Bug Fix
 * This test simulates the exact scenario that was causing the bug
 */

import webRTCConversationService from '../WebRTCConversationService';

// Mock dependencies
jest.mock('expo-audio', () => ({
  AudioModule: {
    setAudioModeAsync: jest.fn().mockResolvedValue(undefined),
    getAvailableOutputsAsync: jest.fn().mockResolvedValue([
      { type: 'speaker', name: 'Speaker', selected: true },
    ]),
    selectAudioOutput: jest.fn().mockResolvedValue(undefined),
  },
}));

jest.mock('react-native-webrtc', () => ({
  mediaDevices: {
    getUserMedia: jest.fn().mockResolvedValue({
      getTracks: () => [{ stop: jest.fn() }],
    }),
  },
  RTCPeerConnection: jest.fn().mockImplementation(() => ({
    createOffer: jest.fn().mockResolvedValue({ sdp: 'mock-sdp' }),
    setLocalDescription: jest.fn().mockResolvedValue(undefined),
    setRemoteDescription: jest.fn().mockResolvedValue(undefined),
    addTrack: jest.fn(),
    close: jest.fn(),
    createDataChannel: jest.fn().mockReturnValue({
      onopen: null,
      onmessage: null,
      onerror: null,
      onclose: null,
      send: jest.fn(),
      close: jest.fn(),
      readyState: 'open',
    }),
    iceConnectionState: 'new',
    oniceconnectionstatechange: null,
    ontrack: null,
  })),
  RTCSessionDescription: jest.fn().mockImplementation((desc) => desc),
}));

describe('WebRTC Conversation Bug Fix', () => {
  beforeEach(() => {
    webRTCConversationService.removeAllListeners();
    jest.clearAllMocks();
  });

  afterEach(async () => {
    await webRTCConversationService.destroy();
  });

  test('should handle multiple conversation sessions without getting stuck on initializing', async () => {
    console.log('🧪 Testing multiple conversation sessions...');

    // FIRST CONVERSATION SESSION
    console.log('1️⃣ Starting first conversation...');
    
    // Initialize service
    const initResult1 = await webRTCConversationService.initialize();
    expect(initResult1).toBe(true);
    
    // Start first session
    const sessionResult1 = await webRTCConversationService.startSession('restaurant-ordering', 'beginner');
    expect(sessionResult1).toBe(true);
    
    const state1 = webRTCConversationService.getState();
    expect(state1.isSessionActive).toBe(true);
    expect(state1.currentScenario).toBe('restaurant-ordering');
    
    console.log('✅ First conversation started successfully');
    
    // Stop first session
    await webRTCConversationService.stopSessionByUser();
    
    const stateAfterStop = webRTCConversationService.getState();
    expect(stateAfterStop.isSessionActive).toBe(false);
    expect(stateAfterStop.userEndedSession).toBe(true);
    
    console.log('✅ First conversation stopped successfully');
    
    // SECOND CONVERSATION SESSION (This is where the bug occurred)
    console.log('2️⃣ Starting second conversation...');
    
    // Clear all data (simulates what happens in the UI)
    await webRTCConversationService.destroy();
    
    // Small delay to simulate real-world timing
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Try to initialize again (this should work now with our fix)
    const initResult2 = await webRTCConversationService.initialize();
    expect(initResult2).toBe(true);
    
    // Start second session
    const sessionResult2 = await webRTCConversationService.startSession('shopping-mall', 'intermediate');
    expect(sessionResult2).toBe(true);
    
    const state2 = webRTCConversationService.getState();
    expect(state2.isSessionActive).toBe(true);
    expect(state2.currentScenario).toBe('shopping-mall');
    expect(state2.currentLevel).toBe('intermediate');
    
    console.log('✅ Second conversation started successfully');
    
    // THIRD CONVERSATION SESSION (Extra verification)
    console.log('3️⃣ Starting third conversation...');
    
    // Stop second session
    await webRTCConversationService.stopSessionByUser();
    
    // Clear and restart again
    await webRTCConversationService.destroy();
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Initialize and start third session
    const initResult3 = await webRTCConversationService.initialize();
    expect(initResult3).toBe(true);
    
    const sessionResult3 = await webRTCConversationService.startSession('airport-checkin', 'advanced');
    expect(sessionResult3).toBe(true);
    
    const state3 = webRTCConversationService.getState();
    expect(state3.isSessionActive).toBe(true);
    expect(state3.currentScenario).toBe('airport-checkin');
    expect(state3.currentLevel).toBe('advanced');
    
    console.log('✅ Third conversation started successfully');
    console.log('🎉 Bug fix verified - multiple conversations work correctly!');
  });

  test('should properly reset initialization state after destroy', async () => {
    console.log('🧪 Testing initialization state reset...');
    
    // Initialize service
    await webRTCConversationService.initialize();
    let state = webRTCConversationService.getState();
    
    // Start a session
    await webRTCConversationService.startSession('test-scenario', 'beginner');
    
    // Destroy service
    await webRTCConversationService.destroy();
    
    // Verify state is reset
    state = webRTCConversationService.getState();
    expect(state.isSessionActive).toBe(false);
    expect(state.isConnected).toBe(false);
    expect(state.currentScenario).toBe(null);
    
    // Should be able to initialize again
    const reinitResult = await webRTCConversationService.initialize();
    expect(reinitResult).toBe(true);
    
    console.log('✅ Initialization state reset correctly');
  });

  test('should handle rapid session transitions', async () => {
    console.log('🧪 Testing rapid session transitions...');
    
    await webRTCConversationService.initialize();
    
    // Start and stop sessions rapidly
    for (let i = 0; i < 3; i++) {
      console.log(`🔄 Rapid transition ${i + 1}/3`);
      
      const sessionResult = await webRTCConversationService.startSession(`scenario-${i}`, 'beginner');
      expect(sessionResult).toBe(true);
      
      const state = webRTCConversationService.getState();
      expect(state.isSessionActive).toBe(true);
      expect(state.currentScenario).toBe(`scenario-${i}`);
      
      await webRTCConversationService.stopSession();
      
      const stoppedState = webRTCConversationService.getState();
      expect(stoppedState.isSessionActive).toBe(false);
    }
    
    console.log('✅ Rapid session transitions handled correctly');
  });
});
