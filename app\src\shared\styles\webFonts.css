/**
 * Web Font Loading for Montserrat
 * Loads all required Montserrat font weights for web compatibility
 * Based on UNextDoor brand guidelines
 */

/* Import Montserrat from Google Fonts with all required weights */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,400&display=swap');

/* Font face declarations for better control */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/montserrat/v26/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCtr6Ew-.woff2') format('woff2');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/montserrat/v26/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCtr6Ew-.woff2') format('woff2');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/montserrat/v26/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCtr6Ew-.woff2') format('woff2');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/montserrat/v26/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCtr6Ew-.woff2') format('woff2');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/montserrat/v26/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCtr6Ew-.woff2') format('woff2');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/montserrat/v26/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCtr6Ew-.woff2') format('woff2');
}

@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/montserrat/v26/JTUFjIg1_i6t8kCHKm459Wx7xQYXK0vOoz6jq6R8aX8.woff2') format('woff2');
}

/* CSS Custom Properties for font weights */
:root {
  --font-montserrat-light: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-montserrat-regular: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-montserrat-medium: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-montserrat-semibold: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-montserrat-bold: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-montserrat-extrabold: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-montserrat-italic: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  
  /* Miles-Inspired Brand Colors */
  --color-primary: #5BC4B3;
  --color-secondary: #36798A;
  --color-tertiary: #4F4F4F;
  --color-accent: #A3E8DC;
  --color-canvas: #F7E7C1;
  --color-brown: #A46E3E;
  --color-white: #FAFAFA;
}

/* Base font styles */
* {
  font-family: var(--font-montserrat-regular);
}

/* Font weight utility classes */
.font-light {
  font-family: var(--font-montserrat-light);
  font-weight: 300;
}

.font-regular {
  font-family: var(--font-montserrat-regular);
  font-weight: 400;
}

.font-medium {
  font-family: var(--font-montserrat-medium);
  font-weight: 500;
}

.font-semibold {
  font-family: var(--font-montserrat-semibold);
  font-weight: 600;
}

.font-bold {
  font-family: var(--font-montserrat-bold);
  font-weight: 700;
}

.font-extrabold {
  font-family: var(--font-montserrat-extrabold);
  font-weight: 800;
}

.font-italic {
  font-family: var(--font-montserrat-italic);
  font-style: italic;
}

/* Brand color utility classes */
.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-secondary);
}

.text-tertiary {
  color: var(--color-tertiary);
}

.bg-primary {
  background-color: var(--color-primary);
}

.bg-secondary {
  background-color: var(--color-secondary);
}

.bg-tertiary {
  background-color: var(--color-tertiary);
}

/* Ensure proper font rendering */
body {
  font-family: var(--font-montserrat-regular);
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Headings with proper font weights */
h1, h2 {
  font-family: var(--font-montserrat-bold);
  font-weight: 700;
}

h3, h4, h5, h6 {
  font-family: var(--font-montserrat-semibold);
  font-weight: 600;
}

/* Button and input font inheritance */
button, input, textarea, select {
  font-family: inherit;
  font-weight: inherit;
}
