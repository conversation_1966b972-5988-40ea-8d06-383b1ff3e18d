{"cli": {"version": ">= 16.4.1", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"gradleCommand": ":app:assembleDebug", "buildType": "apk"}, "ios": {"buildConfiguration": "Debug"}, "env": {"API_BASE_URL": "http://192.168.1.7:5001/api/v1", "WS_BASE_URL": "ws://192.168.1.7:5001"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk"}, "env": {"API_BASE_URL": "http://192.168.1.7:5001/api/v1", "WS_BASE_URL": "ws://192.168.1.7:5001"}}, "production": {"autoIncrement": true, "env": {"API_BASE_URL": "https://api.UNextDoor.com/api/v1", "WS_BASE_URL": "wss://api.UNextDoor.com"}}}, "submit": {"production": {}}}